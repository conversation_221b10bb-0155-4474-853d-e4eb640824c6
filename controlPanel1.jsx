// After Effects script to generate a complex futuristic control panel UI
// Designed for a starship cockpit panel, vertical orientation
// Author: ChatGPT

(function createFuturisticCockpitPanel() {
    var compWidth = 1080;
    var compHeight = 1920;
    var compName = "FuturisticCockpitPanel";
    var compDuration = 30;
    var compFPS = 30;

    var project = app.project;
    app.beginUndoGroup("Create Futuristic Cockpit Panel");

    if (!project) project = app.newProject();

    // Create the composition
    var comp = project.items.addComp(compName, compWidth, compHeight, 1, compDuration, compFPS);

    // Create dark background frame
    var bgSolid = comp.layers.addSolid([0.05, 0.05, 0.08], "Background", compWidth, compHeight, 1);
    bgSolid.blendingMode = BlendingMode.NORMAL;

    // Function to add blinking indicator
    function addBlinkingLight(name, x, y, color, blinkDuration, blinkStartOffset) {
        var light = comp.layers.addSolid(color, name, 50, 50, 1);
        light.property("Position").setValue([x, y]);
        var opacity = light.property("Opacity");
        var expr = `freq = 1/${blinkDuration};\nphase = time + ${blinkStartOffset};\nMath.round(Math.sin(freq * phase * 2 * Math.PI)) * 100`; 
        opacity.expression = expr;
        return light;
    }

    // Add various blinking lights
    var lightColors = [[1, 0, 0], [0, 1, 0], [1, 1, 0], [0, 1, 1]];
    for (var i = 0; i < 8; i++) {
        addBlinkingLight("Indicator_" + i, 100 + (i % 4) * 100, 200 + Math.floor(i / 4) * 100, lightColors[i % lightColors.length], 0.5 + (i % 3) * 0.3, i * 0.2);
    }

    // Add switches (simulate toggle switches)
    function addSwitch(name, x, y) {
        var base = comp.layers.addSolid([0.2, 0.2, 0.2], name + "_Base", 60, 20, 1);
        base.property("Position").setValue([x, y]);

        var toggle = comp.layers.addSolid([0.7, 0.7, 0.7], name + "_Toggle", 20, 40, 1);
        toggle.moveBefore(base);
        toggle.property("Position").setValue([x, y - 10]);
        toggle.property("Rotation").expression = "wiggle(0.5, 10)";
    }

    for (var j = 0; j < 5; j++) {
        addSwitch("Switch_" + j, 200 + j * 120, 600);
    }

    // Add label text for realism
    function addLabel(text, x, y) {
        var label = comp.layers.addText(text);
        var textProp = label.property("Source Text");
        var textDocument = textProp.value;
        textDocument.fontSize = 18;
        textDocument.fillColor = [0.6, 0.9, 1];
        textDocument.justification = ParagraphJustification.CENTER_JUSTIFY;
        textProp.setValue(textDocument);
        label.property("Position").setValue([x, y]);
    }

    addLabel("NAV", 100, 400);
    addLabel("ENG", 200, 400);
    addLabel("COM", 300, 400);
    addLabel("LIFE", 400, 400);

    app.endUndoGroup();
})();
