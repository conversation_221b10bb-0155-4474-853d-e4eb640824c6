/**
 * After Effects Script: Futuristic Starship Control Panel Generator
 *
 * This script generates a complex, futuristic control panel for a starship cockpit.
 * It features a user interface with sliders to control the number of lights and switches.
 * It creates a dark, vertically-oriented frame with various blinking lights and aircraft-style switches.
 *
 * v2.3 Update: Fixed a critical error in adding the final glow effect that was halting script execution.
 *
 * Instructions:
 * 1. Open Adobe After Effects.
 * 2. Go to File > Scripts > Run Script File...
 * 3. Select this script file.
 * 4. A dialog box will appear. Use the sliders to choose the number of lights and switches.
 * 5. Click "Generate".
 * 6. The script will create a new composition named "Starship_Control_Panel" with the generated elements.
 * 7. If an error occurs, an alert box will describe the issue.
 */

(function createFuturisticControlPanelUI() {

    // --- MAIN GENERATION FUNCTION ---
    function generatePanel(numLights, numSwitches) {
        try {
            app.beginUndoGroup("Create Control Panel");

            // --- SETUP ---
            var compName = "Starship_Control_Panel";
            var compWidth = 1080;
            var compHeight = 1920;
            var compDuration = 30; // in seconds
            var compFrameRate = 30;
            var mainComp = app.project.items.addComp(compName, compWidth, compHeight, 1, compDuration, compFrameRate);

            // --- PANEL FRAME ---
            var panelColor = [0.05, 0.05, 0.05]; // Dark gray
            mainComp.layers.addSolid(panelColor, "Panel_Frame", compWidth, compHeight, 1, compDuration);

            // --- LIGHTS ---
            function createLight(name, position, color, size, blinkType) {
                var lightLayer = mainComp.layers.addSolid(color, name, size, size, 1, compDuration);
                lightLayer.position.setValue(position);
                lightLayer.property("Effects").addProperty("ADBE Glo2"); // Add Glow effect
                lightLayer.effect("Glow")("Glow Radius").setValue(size * 1.5);
                lightLayer.effect("Glow")("Glow Intensity").setValue(0.5);

                // Add blinking expressions
                switch (blinkType) {
                    case "fast":
                        lightLayer.opacity.expression = "Math.sin(time * 20) * 50 + 50;";
                        break;
                    case "slow":
                        lightLayer.opacity.expression = "Math.sin(time * 2) * 40 + 60;";
                        break;
                    case "random":
                        lightLayer.opacity.expression = "seedRandom(index, true); wiggle(5, 50);";
                        break;
                    case "pulse":
                        lightLayer.opacity.expression = "Math.sin(time * 5) * 25 + 75;";
                        break;
                    default: // "on"
                        lightLayer.opacity.setValue(100);
                }
            }

            // Procedurally generate lights
            var lightColors = [[0, 1, 0], [1, 0, 0], [0, 0.5, 1], [1, 0.8, 0], [1, 1, 0]];
            var blinkTypes = ["fast", "slow", "random", "pulse", "on"];
            var lightZone = { x1: compWidth * 0.1, x2: compWidth * 0.9, y1: compHeight * 0.1, y2: compHeight * 0.9 };

            for (var i = 0; i < numLights; i++) {
                var randX = Math.random() * (lightZone.x2 - lightZone.x1) + lightZone.x1;
                var randY = Math.random() * (lightZone.y2 - lightZone.y1) + lightZone.y1;
                var randColor = lightColors[Math.floor(Math.random() * lightColors.length)];
                var randBlink = blinkTypes[Math.floor(Math.random() * blinkTypes.length)];
                var randSize = Math.random() * 25 + 15;
                createLight("Procedural_Light_" + i, [randX, randY], randColor, randSize, randBlink);
            }

            // --- SWITCHES ---
            function createSwitch(name, position, initialState) {
                var switchGroup = mainComp.layers.addNull(compDuration);
                switchGroup.name = name;
                switchGroup.position.setValue(position);

                var switchBase = mainComp.layers.addShape();
                switchBase.name = "Switch_Base";
                switchBase.parent = switchGroup;
                switchBase.content.addProperty("ADBE Vector Shape - Rect").size.setValue([25, 60]);
                switchBase.content.addProperty("ADBE Vector Graphic - Fill").color.setValue([0.2, 0.2, 0.2]);

                var switchToggle = mainComp.layers.addShape();
                switchToggle.name = "Switch_Toggle";
                switchToggle.parent = switchGroup;
                switchToggle.content.addProperty("ADBE Vector Shape - Rect").size.setValue([15, 50]);
                switchToggle.content.addProperty("ADBE Vector Graphic - Fill").color.setValue([0.7, 0.7, 0.7]);
                
                var yOffset = initialState ? -10 : 10;
                switchToggle.position.setValue([0, yOffset]);
            }

            // Procedurally generate switches in a row
            var switchZoneWidth = compWidth * 0.8;
            var switchSpacing = switchZoneWidth / (numSwitches > 1 ? numSwitches - 1 : 1);
            var switchStartX = (compWidth - switchZoneWidth) / 2;
            var switchY = compHeight * 0.75;

            for (var j = 0; j < numSwitches; j++) {
                var switchX = (numSwitches === 1) ? compWidth / 2 : switchStartX + (j * switchSpacing);
                createSwitch("Procedural_Switch_" + (j + 1), [switchX, switchY], Math.random() > 0.5);
            }

            // --- Final Touches ---
            var adjustmentLayer = mainComp.layers.addSolid([0, 0, 0], "Global_Glow", compWidth, compHeight, 1, compDuration);
            adjustmentLayer.adjustmentLayer = true;
            // **FIXED**: Correctly added the "Glow" effect to the layer's "Effects" property group.
            var glow = adjustmentLayer.property("Effects").addProperty("Glow");
            glow.property("Glow Threshold").setValue(75);
            glow.property("Glow Radius").setValue(40);
            glow.property("Glow Intensity").setValue(0.6);

            mainComp.openInViewer();
            app.endUndoGroup();
            alert("Futuristic Control Panel created successfully!");

        } catch (e) {
            alert("An error occurred during script execution:\n" + e.toString());
        }
    }

    // --- SCRIPT UI ---
    var win = new Window("palette", "Panel Generator", undefined);
    win.orientation = "column";
    win.alignChildren = ["fill", "top"];

    // Lights Slider
    var lightsGroup = win.add("panel", undefined, "Lights");
    lightsGroup.orientation = "row";
    lightsGroup.add("statictext", undefined, "Number:");
    var lightsSlider = lightsGroup.add("slider", undefined, 20, 1, 100);
    var lightsValueText = lightsGroup.add("statictext", undefined, Math.round(lightsSlider.value));
    lightsValueText.characters = 3;
    lightsSlider.onChanging = function() {
        lightsValueText.text = Math.round(this.value);
    }

    // Switches Slider
    var switchesGroup = win.add("panel", undefined, "Switches");
    switchesGroup.orientation = "row";
    switchesGroup.add("statictext", undefined, "Number:");
    var switchesSlider = switchesGroup.add("slider", undefined, 5, 1, 20);
    var switchesValueText = switchesGroup.add("statictext", undefined, Math.round(switchesSlider.value));
    switchesValueText.characters = 3;
    switchesSlider.onChanging = function() {
        switchesValueText.text = Math.round(this.value);
    }

    // Action Buttons
    var btnGroup = win.add("group");
    btnGroup.orientation = "row";
    var generateBtn = btnGroup.add("button", undefined, "Generate");
    var cancelBtn = btnGroup.add("button", undefined, "Cancel");

    generateBtn.onClick = function() {
        var numLights = Math.round(lightsSlider.value);
        var numSwitches = Math.round(switchesSlider.value);
        win.close();
        generatePanel(numLights, numSwitches);
    };

    cancelBtn.onClick = function() {
        win.close();
    };

    win.center();
    win.show();

})();
