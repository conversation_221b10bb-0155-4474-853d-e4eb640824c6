/**
 * After Effects Script: Futuristic Starship Control Panel Generator
 *
 * This script generates a complex, futuristic control panel for a starship cockpit.
 * It features a user interface with sliders to control the number of lights and switches.
 * It creates a dark, vertically-oriented frame with various blinking lights and aircraft-style switches.
 *
 * v2.4 Update: Corrected the final "Glow" effect call to use its internal "Match Name" ("ADBE Glo2")
 * instead of its display name ("Glow"), which was causing the "unable to call 'addProperty'" error.
 *
 * Instructions:
 * 1. Open Adobe After Effects.
 * 2. Go to File > Scripts > Run Script File...
 * 3. Select this script file.
 * 4. A dialog box will appear. Use the sliders to choose the number of lights and switches.
 * 5. Click "Generate".
 * 6. The script will create a new composition named "Starship_Control_Panel" with the generated elements.
 */

(function createFuturisticControlPanelUI() {

    // --- MAIN GENERATION FUNCTION ---
    function generatePanel(numLights, numSwitches) {
        try {
            app.beginUndoGroup("Create Control Panel");

            // --- SETUP ---
            var compName = "Starship_Control_Panel";
            var compWidth = 1080;
            var compHeight = 1920;
            var compDuration = 30; // in seconds
            var compFrameRate = 30;
            var mainComp = app.project.items.addComp(compName, compWidth, compHeight, 1, compDuration, compFrameRate);

            // --- PANEL FRAME WITH DEPTH ---
            // Background wall
            var wallColor = [0.15, 0.15, 0.15]; // Lighter gray for wall
            mainComp.layers.addSolid(wallColor, "Wall_Background", compWidth, compHeight, 1, compDuration);

            // Frame dimensions
            var frameThickness = 40;
            var innerWidth = compWidth - (frameThickness * 2);
            var innerHeight = compHeight - (frameThickness * 2);

            // Create beveled frame pieces for depth illusion

            // Top bevel (light highlight)
            var topBevel = mainComp.layers.addShape();
            topBevel.name = "Frame_Top_Bevel";
            var topBevelPath = topBevel.content.addProperty("ADBE Vector Shape - Group");
            var topBevelShape = topBevelPath.content.addProperty("ADBE Vector Shape - Shape");
            var topBevelVertices = [
                [0, 0],
                [compWidth, 0],
                [compWidth - frameThickness, frameThickness],
                [frameThickness, frameThickness]
            ];
            topBevelShape.property("ADBE Vector Shape - Vertices").setValue(topBevelVertices);
            var topBevelFill = topBevelPath.content.addProperty("ADBE Vector Graphic - Fill");
            topBevelFill.property("ADBE Vector Fill - Color").setValue([0.4, 0.4, 0.4]); // Light gray highlight

            // Left bevel (medium highlight)
            var leftBevel = mainComp.layers.addShape();
            leftBevel.name = "Frame_Left_Bevel";
            var leftBevelPath = leftBevel.content.addProperty("ADBE Vector Shape - Group");
            var leftBevelShape = leftBevelPath.content.addProperty("ADBE Vector Shape - Shape");
            var leftBevelVertices = [
                [0, 0],
                [frameThickness, frameThickness],
                [frameThickness, compHeight - frameThickness],
                [0, compHeight]
            ];
            leftBevelShape.property("ADBE Vector Shape - Vertices").setValue(leftBevelVertices);
            var leftBevelFill = leftBevelPath.content.addProperty("ADBE Vector Graphic - Fill");
            leftBevelFill.property("ADBE Vector Fill - Color").setValue([0.3, 0.3, 0.3]); // Medium gray

            // Right bevel (dark shadow)
            var rightBevel = mainComp.layers.addShape();
            rightBevel.name = "Frame_Right_Bevel";
            var rightBevelPath = rightBevel.content.addProperty("ADBE Vector Shape - Group");
            var rightBevelShape = rightBevelPath.content.addProperty("ADBE Vector Shape - Shape");
            var rightBevelVertices = [
                [compWidth, 0],
                [compWidth, compHeight],
                [compWidth - frameThickness, compHeight - frameThickness],
                [compWidth - frameThickness, frameThickness]
            ];
            rightBevelShape.property("ADBE Vector Shape - Vertices").setValue(rightBevelVertices);
            var rightBevelFill = rightBevelPath.content.addProperty("ADBE Vector Graphic - Fill");
            rightBevelFill.property("ADBE Vector Fill - Color").setValue([0.08, 0.08, 0.08]); // Dark shadow

            // Bottom bevel (darkest shadow)
            var bottomBevel = mainComp.layers.addShape();
            bottomBevel.name = "Frame_Bottom_Bevel";
            var bottomBevelPath = bottomBevel.content.addProperty("ADBE Vector Shape - Group");
            var bottomBevelShape = bottomBevelPath.content.addProperty("ADBE Vector Shape - Shape");
            var bottomBevelVertices = [
                [0, compHeight],
                [frameThickness, compHeight - frameThickness],
                [compWidth - frameThickness, compHeight - frameThickness],
                [compWidth, compHeight]
            ];
            bottomBevelShape.property("ADBE Vector Shape - Vertices").setValue(bottomBevelVertices);
            var bottomBevelFill = bottomBevelPath.content.addProperty("ADBE Vector Graphic - Fill");
            bottomBevelFill.property("ADBE Vector Fill - Color").setValue([0.05, 0.05, 0.05]); // Darkest shadow

            // Inner panel (recessed area)
            var panelColor = [0.1, 0.1, 0.1]; // Dark gray for recessed panel
            var innerPanel = mainComp.layers.addSolid(panelColor, "Inner_Panel", innerWidth, innerHeight, 1, compDuration);
            innerPanel.position.setValue([compWidth/2, compHeight/2]);

            // --- LIGHTS ---
            function createLight(name, position, color, size, blinkType) {
                var lightLayer = mainComp.layers.addSolid(color, name, Math.round(size), Math.round(size), 1, compDuration);
                lightLayer.position.setValue(position);
                lightLayer.property("Effects").addProperty("ADBE Glo2"); // Add Glow effect using Match Name
                lightLayer.effect("Glow")("Glow Radius").setValue(size * 1.5);
                lightLayer.effect("Glow")("Glow Intensity").setValue(0.5);

                // Add blinking expressions
                switch (blinkType) {
                    case "fast":
                        lightLayer.opacity.expression = "Math.sin(time * 20) * 50 + 50;";
                        break;
                    case "slow":
                        lightLayer.opacity.expression = "Math.sin(time * 2) * 40 + 60;";
                        break;
                    case "random":
                        lightLayer.opacity.expression = "seedRandom(index, true); wiggle(5, 50);";
                        break;
                    case "pulse":
                        lightLayer.opacity.expression = "Math.sin(time * 5) * 25 + 75;";
                        break;
                    default: // "on"
                        lightLayer.opacity.setValue(100);
                }
            }

            // Generate lights in organized grid blocks
            var lightColors = [[0, 1, 0], [1, 0, 0], [0, 0.5, 1], [1, 0.8, 0], [1, 1, 0]];
            var blinkTypes = ["fast", "slow", "random", "pulse", "on"];

            // Define grid parameters (adjusted for frame)
            var gridMargin = frameThickness + (innerWidth * 0.1);
            var gridWidth = innerWidth - (2 * (innerWidth * 0.1));
            var gridHeight = innerHeight * 0.5; // Use upper half for lights
            var gridStartY = frameThickness + (innerHeight * 0.1);

            // Calculate optimal grid dimensions
            var cols = Math.ceil(Math.sqrt(numLights * (gridWidth / gridHeight)));
            var rows = Math.ceil(numLights / cols);

            // Calculate spacing
            var cellWidth = gridWidth / cols;
            var cellHeight = gridHeight / rows;
            var lightSize = Math.min(cellWidth, cellHeight) * 0.4; // 40% of cell size

            for (var i = 0; i < numLights; i++) {
                var col = i % cols;
                var row = Math.floor(i / cols);

                var lightX = gridMargin + (col * cellWidth) + (cellWidth / 2);
                var lightY = gridStartY + (row * cellHeight) + (cellHeight / 2);

                var randColor = lightColors[Math.floor(Math.random() * lightColors.length)];
                var randBlink = blinkTypes[Math.floor(Math.random() * blinkTypes.length)];

                createLight("Grid_Light_" + i, [lightX, lightY], randColor, lightSize, randBlink);
            }

            // --- SWITCHES ---
            function createSwitch(name, position, initialState) {
                var switchGroup = mainComp.layers.addNull(compDuration);
                switchGroup.name = name;
                switchGroup.position.setValue(position);

                var switchBase = mainComp.layers.addShape();
                switchBase.name = "Switch_Base";
                switchBase.parent = switchGroup;
                switchBase.content.addProperty("ADBE Vector Shape - Rect").size.setValue([25, 60]);
                switchBase.content.addProperty("ADBE Vector Graphic - Fill").color.setValue([0.2, 0.2, 0.2]);

                var switchToggle = mainComp.layers.addShape();
                switchToggle.name = "Switch_Toggle";
                switchToggle.parent = switchGroup;
                switchToggle.content.addProperty("ADBE Vector Shape - Rect").size.setValue([15, 50]);
                switchToggle.content.addProperty("ADBE Vector Graphic - Fill").color.setValue([0.7, 0.7, 0.7]);
                
                var yOffset = initialState ? -10 : 10;
                switchToggle.position.setValue([0, yOffset]);
            }

            // Procedurally generate switches in a row (adjusted for frame)
            var switchZoneWidth = innerWidth * 0.8;
            var switchSpacing = switchZoneWidth / (numSwitches > 1 ? numSwitches - 1 : 1);
            var switchStartX = frameThickness + ((innerWidth - switchZoneWidth) / 2);
            var switchY = frameThickness + (innerHeight * 0.75);

            for (var j = 0; j < numSwitches; j++) {
                var switchX = (numSwitches === 1) ? compWidth / 2 : switchStartX + (j * switchSpacing);
                createSwitch("Procedural_Switch_" + (j + 1), [switchX, switchY], Math.random() > 0.5);
            }

            // --- Final Touches ---
            var adjustmentLayer = mainComp.layers.addSolid([0, 0, 0], "Global_Glow", compWidth, compHeight, 1, compDuration);
            adjustmentLayer.adjustmentLayer = true;
            // **FIXED**: Changed "Glow" to its internal Match Name "ADBE Glo2" to prevent errors.
            var glow = adjustmentLayer.property("Effects").addProperty("ADBE Glo2");
            glow.property("Glow Threshold").setValue(75);
            glow.property("Glow Radius").setValue(40);
            glow.property("Glow Intensity").setValue(0.6);

            mainComp.openInViewer();
            app.endUndoGroup();
            alert("Futuristic Control Panel created successfully!");

        } catch (e) {
            alert("An error occurred during script execution:\n" + e.toString());
        }
    }

    // --- SCRIPT UI ---
    var win = new Window("palette", "Panel Generator", undefined);
    win.orientation = "column";
    win.alignChildren = ["fill", "top"];

    // Lights Slider
    var lightsGroup = win.add("panel", undefined, "Lights");
    lightsGroup.orientation = "row";
    lightsGroup.add("statictext", undefined, "Number:");
    var lightsSlider = lightsGroup.add("slider", undefined, 20, 1, 100);
    var lightsValueText = lightsGroup.add("statictext", undefined, Math.round(lightsSlider.value));
    lightsValueText.characters = 3;
    lightsSlider.onChanging = function() {
        lightsValueText.text = Math.round(this.value);
    }

    // Switches Slider
    var switchesGroup = win.add("panel", undefined, "Switches");
    switchesGroup.orientation = "row";
    switchesGroup.add("statictext", undefined, "Number:");
    var switchesSlider = switchesGroup.add("slider", undefined, 5, 1, 20);
    var switchesValueText = switchesGroup.add("statictext", undefined, Math.round(switchesSlider.value));
    switchesValueText.characters = 3;
    switchesSlider.onChanging = function() {
        switchesValueText.text = Math.round(this.value);
    }

    // Action Buttons
    var btnGroup = win.add("group");
    btnGroup.orientation = "row";
    var generateBtn = btnGroup.add("button", undefined, "Generate");
    var cancelBtn = btnGroup.add("button", undefined, "Cancel");

    generateBtn.onClick = function() {
        var numLights = Math.round(lightsSlider.value);
        var numSwitches = Math.round(switchesSlider.value);
        win.close();
        generatePanel(numLights, numSwitches);
    };

    cancelBtn.onClick = function() {
        win.close();
    };

    win.center();
    win.show();

})();
